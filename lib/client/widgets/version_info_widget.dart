import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Widget that displays app version information
/// Useful for debugging and ensuring the latest version is deployed
class VersionInfoWidget extends StatefulWidget {
  final bool showBuildNumber;
  final bool showAppName;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final EdgeInsets? padding;
  final bool showTimestamp;

  const VersionInfoWidget({
    super.key,
    this.showBuildNumber = true,
    this.showAppName = false,
    this.textStyle,
    this.backgroundColor,
    this.padding,
    this.showTimestamp = true,
  });

  @override
  State<VersionInfoWidget> createState() => _VersionInfoWidgetState();
}

class _VersionInfoWidgetState extends State<VersionInfoWidget> {
  PackageInfo? _packageInfo;
  String? _buildTimestamp;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      // Use current time as a "session start" timestamp to identify when the app was loaded
      final buildTimestamp = DateTime.now().toIso8601String().substring(0, 19).replaceAll('T', ' ');

      if (mounted) {
        setState(() {
          _packageInfo = packageInfo;
          _buildTimestamp = buildTimestamp;
        });
      }
    } catch (e) {
      print('Error loading package info: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_packageInfo == null) {
      return const SizedBox.shrink();
    }

    final List<String> versionParts = [];

    if (widget.showAppName) {
      versionParts.add(_packageInfo!.appName);
    }

    versionParts.add('v${_packageInfo!.version}');

    if (widget.showBuildNumber) {
      versionParts.add('(${_packageInfo!.buildNumber})');
    }

    if (widget.showTimestamp && _buildTimestamp != null) {
      // Show just the time part for compact display
      final timePart = _buildTimestamp!.split(' ').length > 1
          ? _buildTimestamp!.split(' ')[1].substring(0, 8) // HH:MM:SS
          : _buildTimestamp!.substring(11, 19); // fallback
      versionParts.add('[$timePart]');
    }

    final versionText = versionParts.join(' ');

    return Container(
      padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: widget.backgroundColor != null
          ? BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(4),
            )
          : null,
      child: Text(
        versionText,
        style: widget.textStyle ??
            TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontFamily: 'monospace',
            ),
      ),
    );
  }
}

/// Compact version info for debug purposes
class CompactVersionInfo extends StatelessWidget {
  const CompactVersionInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return const VersionInfoWidget(
      showAppName: false,
      showBuildNumber: true,
      showTimestamp: true,
      textStyle: TextStyle(
        fontSize: 10,
        color: Colors.grey,
        fontFamily: 'monospace',
      ),
      padding: EdgeInsets.all(4),
    );
  }
}

/// Detailed version info for settings or about screens
class DetailedVersionInfo extends StatelessWidget {
  const DetailedVersionInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return const VersionInfoWidget(
      showAppName: true,
      showBuildNumber: true,
      showTimestamp: true,
      textStyle: TextStyle(
        fontSize: 14,
        color: Colors.black87,
        fontFamily: 'monospace',
      ),
      backgroundColor: Colors.white,
      padding: EdgeInsets.all(12),
    );
  }
}
