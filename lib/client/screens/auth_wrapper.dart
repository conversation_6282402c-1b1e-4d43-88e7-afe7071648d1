
import 'dart:js' as js;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:scrum_poker/business/models/woven_user.dart';
import 'package:scrum_poker/client/connectors/home_connector.dart';
import 'package:scrum_poker/client/screens/unified_auth_screen.dart';
import 'package:scrum_poker/client/connectors/team_setup_connector.dart';
import 'package:scrum_poker/services/unified_auth_manager.dart';
import 'package:scrum_poker/client/widgets/version_info_widget.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _initialized = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    if (!mounted) return;

    try {
      print('AuthWrapper: Starting authentication initialization...');

      // Initialize unified auth manager
      await UnifiedAuthManager().initialize();

      if (mounted) {
        setState(() {
          _initialized = true;
          _error = null;
        });
        print('AuthWrapper: Authentication initialization completed');
        print('AuthWrapper: Current user: ${UnifiedAuthManager().currentUser?.toString()}');
        print('AuthWrapper: Current method: ${UnifiedAuthManager().currentMethod.name}');
      }
    } catch (e) {
      print('AuthWrapper: Error during auth initialization: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _initialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show unified auth screen while initializing or if there's an error
    if (!_initialized) {
      return UnifiedAuthScreen(
        isInitializing: true,
        error: _error,
        onRetry: () {
          setState(() {
            _error = null;
            _initialized = false;
          });
          _initializeAuth();
        },
      );
    }

    // If there's an error after initialization, show error state
    if (_error != null) {
      return UnifiedAuthScreen(
        isInitializing: false,
        error: _error,
        onRetry: () {
          setState(() {
            _error = null;
            _initialized = false;
          });
          _initializeAuth();
        },
      );
    }

    // Use stream builder to react to auth state changes
    return StreamBuilder<WovenUser?>(
      stream: UnifiedAuthManager().authStateChanges,
      initialData: UnifiedAuthManager().currentUser,
      builder: (context, snapshot) {
        final user = snapshot.data;
        final authManager = UnifiedAuthManager();

        print('AuthWrapper: StreamBuilder update');
        print('  - User from stream: ${user?.toString()}');
        print('  - Auth method: ${authManager.currentMethod.name}');
        print('  - Stream connection state: ${snapshot.connectionState}');
        print('  - Has error: ${snapshot.hasError}');
        if (snapshot.hasError) {
          print('  - Error: ${snapshot.error}');
        }

        // Show unified auth screen if no user
        if (user == null) {
          // If we're in a Woven App context, show seamless auth loading
          if (_isWovenAppContext()) {
            print('AuthWrapper: Woven App context detected - showing seamless auth loading');

            return UnifiedAuthScreen(
              isInitializing: true,
              authMethod: AuthenticationMethod.seamless,
              onRetry: () async {
                print('AuthWrapper: Retrying seamless authentication...');
                await authManager.reset();
              },
            );
          }

          // Show normal login screen for direct browser access
          return UnifiedAuthScreen(
            isInitializing: false,
            authMethod: authManager.currentMethod,
            onLogin: () async {
              print('AuthWrapper: Login button pressed for ${authManager.currentMethod.name}');
              await authManager.login();
            },
            onSwitchToOAuth: () async {
              print('AuthWrapper: Switching to OAuth authentication');
              await authManager.switchToOAuth();
            },
          );
        }

        // Show team setup if no team or default team
        if (user.team.isEmpty || user.team.toLowerCase() == 'default team') {
          print('AuthWrapper: Showing team setup for user: ${user.name}');
          return const TeamSetupConnector();
        }

        // Show home screen for authenticated users with team
        print('AuthWrapper: Showing home screen for user: ${user.name} (${user.team})');
        return const HomeConnector();
      },
    );
  }

  /// Check if we're in a Woven App context where seamless auth should work
  bool _isWovenAppContext() {
    if (!kIsWeb) return false;

    try {
      // Check if we have OTT in URL (launched from Woven App)
      final currentUrl = Uri.base.toString();
      if (currentUrl.contains('#ott=')) {
        return true;
      }

      // Check if seamless auth was attempted (even if failed)
      if (js.context.hasProperty('seamlessAuthCompleted') ||
          js.context.hasProperty('seamlessAuthFailed')) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Build debug information widget to show on screen
  Widget _buildDebugInfo() {
    if (!kIsWeb) {
      return const Text('Debug: Not running on web');
    }

    try {
      final currentUrl = Uri.base.toString();
      final hasOTT = currentUrl.contains('#ott=');
      final seamlessCompleted = js.context['seamlessAuthCompleted'] as bool?;
      final seamlessFailed = js.context['seamlessAuthFailed'] as bool?;
      final seamlessError = js.context['seamlessAuthError'] as String?;
      final hasAuthClient = js.context.hasProperty('authClient');
      final hasWovenTokens = js.context.hasProperty('wovenTokens');

      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '🔍 DEBUG INFO',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple,
                  ),
                ),
                const CompactVersionInfo(),
              ],
            ),
            const SizedBox(height: 12),
            _debugRow('URL', currentUrl.length > 80 ? '${currentUrl.substring(0, 80)}...' : currentUrl),
            _debugRow('URL Hash', Uri.base.fragment.isEmpty ? 'EMPTY' : Uri.base.fragment),
            _debugRow('Has OTT Token', hasOTT ? '✅ YES' : '❌ NO'),
            _debugRow('Is Woven App Launch', _isWovenAppContext() ? '✅ YES' : '❌ NO'),
            const Divider(),
            _debugRow('Seamless Completed', seamlessCompleted == true ? '✅ YES' : '❌ NO'),
            _debugRow('Seamless Failed', seamlessFailed == true ? '❌ YES' : '✅ NO'),
            if (seamlessError != null) _debugRow('Seamless Error', seamlessError),
            _debugRow('Has Auth Client', hasAuthClient ? '✅ YES' : '❌ NO'),
            _debugRow('Has Woven Tokens', hasWovenTokens ? '✅ YES' : '❌ NO'),
            const Divider(),
            _debugRow('Auth Manager Init', UnifiedAuthManager().isInitialized ? '✅ YES' : '❌ NO'),
            _debugRow('Current Method', UnifiedAuthManager().currentMethod.name.toUpperCase()),
            _debugRow('Current User', UnifiedAuthManager().currentUser?.toString() ?? 'NULL'),
          ],
        ),
      );
    } catch (e) {
      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.red[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[300]!),
        ),
        child: Text(
          '❌ Debug Error: $e',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }
  }

  Widget _debugRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }
}