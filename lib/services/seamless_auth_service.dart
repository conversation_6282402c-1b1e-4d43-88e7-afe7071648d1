import 'dart:async';
import 'dart:js' as js;
import 'dart:convert';
import 'package:async_redux/async_redux.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import 'package:scrum_poker/business/app_state.dart';
import 'package:scrum_poker/business/actions/user/update_current_user_action.dart';
import 'package:scrum_poker/business/models/woven_user.dart';
import 'package:scrum_poker/business/models/user.dart';
import 'package:scrum_poker/services/auth_token_manager.dart';

/// ULTRA-SIMPLIFIED Seamless authentication service
/// ONLY handles real OTT token authentication from Woven App SDK
/// NO mock data, NO OTEL fallbacks, NO fake users
class SeamlessAuthService {
  static final SeamlessAuthService _instance = SeamlessAuthService._internal();
  factory SeamlessAuthService() => _instance;
  SeamlessAuthService._internal();

  WovenUser? _currentUser;
  bool _initialized = false;
  final StreamController<WovenUser?> _authStateController = StreamController<WovenUser?>.broadcast();

  /// Stream of authentication state changes
  Stream<WovenUser?> get authStateChanges => _authStateController.stream;

  /// Current authenticated user
  WovenUser? get currentUser => _currentUser;

  /// Check if the service is initialized
  bool get isInitialized => _initialized;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// ULTRA-SIMPLE initialization - only check for real OTT tokens
  Future<void> initialize() async {
    if (_initialized) return;
    _initialized = true;

    if (!kIsWeb) return;

    try {
      print('=== 🔍 SeamlessAuthService: Analyzing Launch Context ===');

      // Analyze the current URL
      final currentUrl = Uri.base.toString();
      print('🔍 Current URL: $currentUrl');
      print('🔍 URL Hash: ${Uri.base.fragment}');
      print('🔍 URL Query: ${Uri.base.query}');

      // Check for OTT token (real Woven App authentication)
      final hasOTT = currentUrl.contains('#ott=');
      final ottToken = extractOTTFromUrl();
      print('🎫 Has OTT token: $hasOTT');
      if (ottToken != null) {
        print('🎫 OTT token: ${ottToken.substring(0, 20)}...');
      }

      // Check for OTEL parameters (telemetry only, NOT authentication)
      final hasOTEL = currentUrl.contains('otel_');
      print('📊 Has OTEL params: $hasOTEL (these are for telemetry, NOT auth)');

      // Check if JavaScript SDK has already processed the OTT
      final seamlessCompleted = js.context['seamlessAuthCompleted'] as bool?;
      final seamlessFailed = js.context['seamlessAuthFailed'] as bool?;
      final seamlessError = js.context['seamlessAuthError'] as String?;
      final hasWovenTokens = js.context.hasProperty('wovenTokens');

      print('🔍 JavaScript SDK Status:');
      print('  - Completed: $seamlessCompleted');
      print('  - Failed: $seamlessFailed');
      print('  - Error: $seamlessError');
      print('  - Has Woven Tokens: $hasWovenTokens');

      // If JavaScript SDK has already processed authentication successfully
      if (seamlessCompleted == true && hasWovenTokens) {
        print('✅ JavaScript SDK already completed authentication - using existing tokens');

        final wovenTokens = js.context['wovenTokens'];
        if (wovenTokens != null) {
          final accessToken = wovenTokens['accessToken'] as String?;
          final refreshToken = wovenTokens['refreshToken'] as String?;
          final idToken = wovenTokens['idToken'] as String?;

          print('🔑 Token extraction from wovenTokens:');
          print('  - Access Token: ${accessToken != null ? "${accessToken.length} chars" : "NULL"}');
          print('  - Refresh Token: ${refreshToken != null ? "Present" : "NULL"}');
          print('  - ID Token: ${idToken != null ? "Present" : "NULL"}');

          if (accessToken != null && accessToken.isNotEmpty) {
            print('✅ Using tokens from JavaScript SDK - processing now...');
            await _processTokens(accessToken, refreshToken, idToken);
            print('✅ Token processing completed - user should be authenticated');
            return;
          } else {
            print('❌ Access token is null or empty from wovenTokens');
          }
        } else {
          print('❌ wovenTokens object is null');
        }
      } else {
        print('❌ JavaScript SDK authentication not completed or no tokens available');
        print('  - seamlessCompleted: $seamlessCompleted');
        print('  - hasWovenTokens: $hasWovenTokens');
      }

      if (!hasOTT) {
        if (hasOTEL) {
          print('');
          print('🚨 ===============================================');
          print('🚨 WOVEN APP CONFIGURATION ISSUE DETECTED');
          print('🚨 ===============================================');
          print('');
          print('✅ Woven App DID launch this webapp (OTEL params present)');
          print('❌ Woven App did NOT provide authentication (no OTT token)');
          print('');
          print('📋 ISSUE SUMMARY:');
          print('   - OTEL parameters are for telemetry/observability only');
          print('   - Authentication requires an OTT token in URL hash');
          print('   - Expected URL format: https://your-app.com/#ott=token123');
          print('   - Actual URL format: https://your-app.com/?otel_platform_id=123');
          print('');
          print('🔧 SOLUTION:');
          print('   1. Check Woven App mini-app configuration');
          print('   2. Ensure authentication is enabled for this mini-app');
          print('   3. Verify the mini-app is registered correctly');
          print('   4. Contact Woven platform team if needed');
          print('');
          print('⚡ CURRENT BEHAVIOR:');
          print('   - App will fall back to OAuth login (correct behavior)');
          print('   - Users will see login screen instead of seamless auth');
          print('');
          print('🚨 ===============================================');
          print('');
        } else {
          print('❌ No OTT token found - not launched from Woven App');
          print('💡 This is normal for direct browser access');
        }
        return; // No OTT token, no seamless authentication possible
      }

      print('✅ OTT token found - checking if SDK has processed it...');

      // Check if SDK completed authentication (recheck after potential processing)
      final seamlessCompletedFinal = js.context['seamlessAuthCompleted'] as bool?;
      final seamlessFailedFinal = js.context['seamlessAuthFailed'] as bool?;
      final seamlessErrorFinal = js.context['seamlessAuthError'] as String?;

      print('🔍 Final SDK Status:');
      print('  - Completed: $seamlessCompletedFinal');
      print('  - Failed: $seamlessFailedFinal');
      print('  - Error: $seamlessErrorFinal');

      if (seamlessCompletedFinal != true) {
        print('❌ SDK authentication not completed');
        if (seamlessFailedFinal == true) {
          print('❌ SDK authentication failed: $seamlessErrorFinal');
        }
        return; // SDK didn't complete authentication
      }

      print('✅ SDK authentication completed - getting tokens...');

      // Try to get tokens from wovenTokens first (preferred)
      final wovenTokens = js.context['wovenTokens'];
      if (wovenTokens != null) {
        final accessToken = wovenTokens['accessToken'] as String?;
        final refreshToken = wovenTokens['refreshToken'] as String?;
        final idToken = wovenTokens['idToken'] as String?;

        if (accessToken != null && accessToken.isNotEmpty) {
          print('✅ Using tokens from wovenTokens');
          await _processTokens(accessToken, refreshToken, idToken);
          return;
        }
      }

      // Fallback: Get tokens from authClient
      final authClient = js.context['authClient'];
      if (authClient == null) {
        print('❌ No authClient available');
        return;
      }

      if (!authClient.hasProperty('tokens')) {
        print('❌ AuthClient has no tokens property');
        return;
      }

      final sdkTokens = authClient['tokens'];
      if (sdkTokens == null) {
        print('❌ SDK tokens are null');
        return;
      }

      final accessToken = sdkTokens['accessToken'] as String?;
      final refreshToken = sdkTokens['refreshToken'] as String?;
      final idToken = sdkTokens['idToken'] as String?;

      if (accessToken != null && accessToken.isNotEmpty) {
        print('✅ Using tokens from authClient');
        await _processTokens(accessToken, refreshToken, idToken);
      } else {
        print('❌ No valid access token available');
      }

    } catch (e, stackTrace) {
      print('❌ SeamlessAuthService error: $e');
      print('❌ Stack trace: $stackTrace');
      // Silent fail - just don't authenticate
    }
  }

  /// Process tokens and create user
  Future<void> _processTokens(String accessToken, String? refreshToken, String? idToken) async {
    try {
      print('🔑 Processing tokens...');
      print('  - Access Token: ${accessToken.length} chars');
      print('  - Refresh Token: ${refreshToken != null ? "Present" : "Missing"}');
      print('  - ID Token: ${idToken != null ? "Present" : "Missing"}');

      print('🔓 Decoding access token...');

      // Decode token to get user info
      final userInfo = _decodeJWT(accessToken);
      if (userInfo == null) {
        print('❌ Failed to decode JWT token');
        return;
      }

      print('✅ Token decoded successfully');
      print('👤 Token payload: ${userInfo.keys.toList()}');

      final userId = userInfo['sub'] as String?;
      final userName = userInfo['name'] as String? ??
                      userInfo['preferred_username'] as String? ??
                      userInfo['email'] as String? ??
                      'Woven User';

      print('👤 User Info:');
      print('  - ID: $userId');
      print('  - Name: $userName');

      if (userId == null || userId.isEmpty) {
        print('❌ No valid user ID in token');
        return;
      }

      print('✅ Creating WovenUser...');

      // Create user
      _currentUser = WovenUser(
        id: userId,
        name: userName,
        team: 'Woven Team',
        lastActive: DateTime.now(),
      );

      print('✅ WovenUser created: ${_currentUser!.toString()}');

      // Save tokens
      await AuthTokenManager.saveTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
        idToken: idToken,
      );

      print('✅ Tokens saved to AuthTokenManager');

      // Update Redux state
      final store = GetIt.instance.get<Store<AppState>>();
      store.dispatch(UpdateCurrentUserAction(user: _currentUser!.toUser()));

      print('✅ Redux state updated');

      // Notify listeners
      _authStateController.add(_currentUser);

      print('🎉 Seamless authentication completed successfully!');
      print('👤 Final user: ${_currentUser!.name} (${_currentUser!.id})');

    } catch (e, stackTrace) {
      print('❌ Error processing tokens: $e');
      print('❌ Stack trace: $stackTrace');
    }
  }

  /// Decode JWT token to get user information
  Map<String, dynamic>? _decodeJWT(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      return json.decode(decoded) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// Check if we have OTT token in URL
  bool hasOTTInUrl() {
    if (!kIsWeb) return false;
    final currentUrl = Uri.base.toString();
    return currentUrl.contains('#ott=');
  }

  /// Extract OTT token from URL
  String? extractOTTFromUrl() {
    if (!kIsWeb) return null;

    try {
      final hash = Uri.base.fragment;
      if (hash.isEmpty) return null;

      // Parse the hash as URL parameters
      final hashParams = Uri.splitQueryString(hash);
      return hashParams['ott'];
    } catch (e) {
      print('Error extracting OTT from URL: $e');
      return null;
    }
  }

  /// Get current access token
  Future<String?> getAccessToken() async {
    try {
      return await AuthTokenManager.getAccessToken();
    } catch (e) {
      return null;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      _currentUser = null;
      await AuthTokenManager.clearTokens();
      _authStateController.add(null);
    } catch (e) {
      // Silent fail
    }
  }

  /// Dispose resources
  void dispose() {
    _authStateController.close();
  }
}