<!DOCTYPE html>
<html>
<head>
    <title>OTT Parsing Test</title>
    <script src="woven-sdk/auth.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jwt-decode/3.1.2/jwt-decode.min.js"></script>
</head>
<body>
    <h1>OTT Parsing Test</h1>
    <div id="results"></div>

    <script>
        function log(message) {
            console.log(message);
            document.getElementById('results').innerHTML += '<p>' + message + '</p>';
        }

        // Test OTT extraction function
        function extractOTTFromURL() {
            try {
                log('🔍 Extracting OTT from URL...');
                log('🔍 Full URL: ' + window.location.href);
                log('🔍 Hash: ' + window.location.hash);
                
                // Parse the hash fragment
                const hash = window.location.hash;
                if (!hash || hash.length <= 1) {
                    log('❌ No hash fragment found');
                    return null;
                }
                
                // Remove the # and parse as URLSearchParams
                const hashParams = new URLSearchParams(hash.substring(1));
                const ott = hashParams.get('ott');
                
                if (ott) {
                    log('✅ OTT found: ' + ott.substring(0, 20) + '...');
                    return ott;
                } else {
                    log('❌ No OTT parameter in hash');
                    log('🔍 Available hash params: ' + Array.from(hashParams.keys()).join(', '));
                    return null;
                }
            } catch (error) {
                log('❌ Error extracting OTT: ' + error.message);
                return null;
            }
        }

        // Test the OTT parsing
        log('=== OTT Parsing Test ===');
        const ott = extractOTTFromURL();
        
        if (ott) {
            log('✅ OTT extraction successful!');
            log('🎫 Token length: ' + ott.length + ' characters');
            
            // Test if we can create the auth client
            try {
                log('🔄 Testing MiniAppAuthClient creation...');
                const authClient = new window.MiniAppAuthClient({
                    env: 'stage',
                    useAutoRefresh: true
                });
                log('✅ MiniAppAuthClient created successfully');
                log('🔍 Should sign in: ' + authClient.shouldSignInFromWindowLocation());
            } catch (error) {
                log('❌ Error creating auth client: ' + error.message);
            }
        } else {
            log('❌ No OTT found in URL');
            log('💡 To test, add #ott=your_token_here to the URL');
        }

        // Show current URL structure
        log('=== URL Analysis ===');
        log('Protocol: ' + window.location.protocol);
        log('Host: ' + window.location.host);
        log('Pathname: ' + window.location.pathname);
        log('Search: ' + window.location.search);
        log('Hash: ' + window.location.hash);
    </script>
</body>
</html>
