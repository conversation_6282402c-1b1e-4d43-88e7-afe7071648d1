import 'dart:async';
import 'dart:js' as js;

import 'package:flutter/foundation.dart';
import 'package:scrum_poker/business/models/woven_user.dart';
import 'package:scrum_poker/services/auth_service.dart';
import 'package:scrum_poker/services/seamless_auth_service.dart';

/// Unified authentication manager that handles both seamless and direct OAuth flows
///
/// This manager determines which authentication method to use based on:
/// 1. Whether the app is launched from Woven App (seamless flow)
/// 2. Whether the user is accessing directly (OAuth flow)
/// 3. Automatic fallback from seamless to OAuth if seamless fails
class UnifiedAuthManager {
  static final UnifiedAuthManager _instance = UnifiedAuthManager._internal();
  factory UnifiedAuthManager() => _instance;
  UnifiedAuthManager._internal();

  final StreamController<WovenUser?> _authStateController = StreamController<WovenUser?>.broadcast();

  AuthService? _authService;
  SeamlessAuthService? _seamlessAuthService;

  AuthenticationMethod _currentMethod = AuthenticationMethod.none;
  AuthenticationMethod _preferredMethod = AuthenticationMethod.none;
  bool _initialized = false;
  bool _seamlessAttempted = false;
  String? _lastError;

  /// Stream of authentication state changes
  Stream<WovenUser?> get authStateChanges => _authStateController.stream;

  /// Current authenticated user
  WovenUser? get currentUser {
    switch (_currentMethod) {
      case AuthenticationMethod.seamless:
        return _seamlessAuthService?.currentUser;
      case AuthenticationMethod.oauth:
        return _authService?.currentUser;
      case AuthenticationMethod.none:
        return null;
    }
  }

  /// Current authentication method being used
  AuthenticationMethod get currentMethod => _currentMethod;

  /// Preferred authentication method (what we initially tried to use)
  AuthenticationMethod get preferredMethod => _preferredMethod;

  /// Last error encountered during authentication
  String? get lastError => _lastError;

  /// Whether the auth manager has completed initialization
  bool get isInitialized => _initialized;

  /// Initialize the unified auth manager with hybrid authentication strategy
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      print('UnifiedAuthManager: Starting initialization...');
      print('UnifiedAuthManager: Current URL: ${kIsWeb ? Uri.base.toString() : 'N/A'}');
      _lastError = null;

      // Step 1: Always attempt seamless authentication first on mobile/web
      // This is more aggressive to catch Woven App contexts that might be missed
      print('UnifiedAuthManager: Attempting seamless authentication first...');

      // Add a brief delay to ensure HTML script has completed
      print('UnifiedAuthManager: Waiting 500ms for JavaScript SDK to complete...');
      await Future.delayed(const Duration(milliseconds: 500));

      final seamlessSuccess = await _attemptSeamlessAuth();

      print('UnifiedAuthManager: Seamless auth result: $seamlessSuccess');
      print('UnifiedAuthManager: Current user after seamless: ${_seamlessAuthService?.currentUser?.toString()}');

      // Brief delay to analyze results
      await Future.delayed(const Duration(milliseconds: 200));

      if (seamlessSuccess) {
        print('UnifiedAuthManager: ✅ Seamless authentication successful - should NOT see login page');
        _initialized = true;
        return;
      } else {
        print('UnifiedAuthManager: ❌ Seamless authentication failed - will show login page');
      }

      // Step 2: Check if we're in a Woven App context for debugging
      final wovenContext = await _detectWovenAppContext();
      print('UnifiedAuthManager: Woven App context detected: $wovenContext');

      if (wovenContext) {
        print('UnifiedAuthManager: Woven context detected but seamless auth failed');
        print('UnifiedAuthManager: Giving seamless auth one more chance...');

        // Give seamless auth one more chance with a longer delay
        await Future.delayed(const Duration(milliseconds: 2000));

        if (_seamlessAuthService?.currentUser != null) {
          print('UnifiedAuthManager: Seamless authentication succeeded on retry');
          _currentMethod = AuthenticationMethod.seamless;
          _authStateController.add(_seamlessAuthService!.currentUser);
          _initialized = true;
          return;
        } else {
          print('UnifiedAuthManager: Seamless auth still failed after retry');
        }
      }

      // Step 3: Fallback to OAuth authentication
      print('UnifiedAuthManager: Initializing OAuth authentication...');
      await _initializeOAuthAuth();

      _initialized = true;
      print('UnifiedAuthManager: Initialization complete using ${_currentMethod.name}');
      print('UnifiedAuthManager: Final current user: ${currentUser?.toString()}');

      // Print debug state for seamless auth service
      if (_seamlessAuthService != null) {
        print('SeamlessAuthService initialized: ${_seamlessAuthService!.isInitialized}');
        print('SeamlessAuthService authenticated: ${_seamlessAuthService!.isAuthenticated}');
        print('SeamlessAuthService current user: ${_seamlessAuthService!.currentUser?.toString()}');
      }
    } catch (e) {
      print('UnifiedAuthManager: Error during initialization: $e');
      _lastError = e.toString();
      _initialized = true;

      // Ensure we have some auth method even if initialization fails
      if (_currentMethod == AuthenticationMethod.none) {
        await _initializeOAuthAuth();
      }
    }
  }



  /// Get current access token
  Future<String?> getAccessToken() async {
    switch (_currentMethod) {
      case AuthenticationMethod.seamless:
        return await _seamlessAuthService?.getAccessToken();
      case AuthenticationMethod.oauth:
        return await _authService?.getAccessToken();
      case AuthenticationMethod.none:
        return null;
    }
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    switch (_currentMethod) {
      case AuthenticationMethod.seamless:
        return _seamlessAuthService?.isAuthenticated ?? false;
      case AuthenticationMethod.oauth:
        return await _authService?.isAuthenticated() ?? false;
      case AuthenticationMethod.none:
        return false;
    }
  }

  /// Initiate login (only for OAuth flow)
  Future<void> login() async {
    if (_currentMethod == AuthenticationMethod.oauth) {
      await _authService?.login();
    } else {
      print('UnifiedAuthManager: Login not available for seamless authentication');
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      switch (_currentMethod) {
        case AuthenticationMethod.seamless:
          await _seamlessAuthService?.signOut();
          break;
        case AuthenticationMethod.oauth:
          await _authService?.signOut();
          break;
        case AuthenticationMethod.none:
          break;
      }

      _authStateController.add(null);
      print('UnifiedAuthManager: User signed out');
    } catch (e) {
      print('UnifiedAuthManager: Error during sign out: $e');
    }
  }

  /// Update user team (works with both auth methods)
  Future<void> updateUserTeam(String team) async {
    try {
      switch (_currentMethod) {
        case AuthenticationMethod.seamless:
          final user = _seamlessAuthService?.currentUser;
          if (user != null) {
            final updatedUser = user.copyWith(team: team);
            await updatedUser.save();
            _authStateController.add(updatedUser);
          }
          break;
        case AuthenticationMethod.oauth:
          await _authService?.updateUserTeam(team);
          break;
        case AuthenticationMethod.none:
          break;
      }
    } catch (e) {
      print('UnifiedAuthManager: Error updating user team: $e');
    }
  }

  /// Detect if we're running in a Woven App context (improved detection)
  Future<bool> _detectWovenAppContext() async {
    try {
      print('UnifiedAuthManager: Detecting Woven App context...');

      // Initialize seamless service first to ensure SDK is available
      _seamlessAuthService ??= SeamlessAuthService();

      // Check 1: OTT token in URL hash (strongest indicator)
      if (kIsWeb) {
        final currentUrl = Uri.base.toString();
        print('UnifiedAuthManager: Current URL: $currentUrl');

        if (currentUrl.contains('#ott=')) {
          print('UnifiedAuthManager: OTT detected in URL - definite Woven App context');
          return true;
        }
      }

      // Check 2: OTT detection using seamless service
      if (_seamlessAuthService!.hasOTTInUrl()) {
        print('UnifiedAuthManager: OTT detected - Woven App context');
        return true;
      }

      // Check 3: JavaScript entry point authentication completed
      if (kIsWeb) {
        try {
          final seamlessCompleted = js.context['seamlessAuthCompleted'] as bool?;
          if (seamlessCompleted == true) {
            print('UnifiedAuthManager: JavaScript entry point auth completed - Woven App context');
            return true;
          }
        } catch (e) {
          print('UnifiedAuthManager: Error checking JavaScript auth status: $e');
        }
      }

      // Check 4: Existing valid Woven tokens
      if (await _hasExistingSeamlessTokens()) {
        print('UnifiedAuthManager: Existing Woven tokens found - Woven App context');
        return true;
      }

      print('UnifiedAuthManager: No Woven App context indicators found');
      return false;
    } catch (e) {
      print('UnifiedAuthManager: Error detecting Woven App context: $e');
      return false;
    }
  }

  /// Check if we have existing seamless tokens
  Future<bool> _hasExistingSeamlessTokens() async {
    try {
      _seamlessAuthService ??= SeamlessAuthService();
      await _seamlessAuthService!.initialize();

      final token = await _seamlessAuthService!.getAccessToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      print('UnifiedAuthManager: Error checking existing seamless tokens: $e');
      return false;
    }
  }



  /// Attempt seamless authentication
  Future<bool> _attemptSeamlessAuth() async {
    _seamlessAttempted = true;

    try {
      print('UnifiedAuthManager: Attempting seamless authentication...');

      _seamlessAuthService ??= SeamlessAuthService();

      // Set method to seamless early to ensure proper state handling
      _currentMethod = AuthenticationMethod.seamless;

      // Listen to seamless auth state changes BEFORE initialization
      _seamlessAuthService!.authStateChanges.listen((user) {
        print('UnifiedAuthManager: Seamless auth state changed: ${user?.toString()}');
        if (_currentMethod == AuthenticationMethod.seamless) {
          _authStateController.add(user);
        }
      });

      await _seamlessAuthService!.initialize();

      // Check if seamless auth was successful immediately after initialization
      if (_seamlessAuthService!.currentUser != null) {
        print('UnifiedAuthManager: Seamless authentication successful immediately');
        _authStateController.add(_seamlessAuthService!.currentUser);
        return true;
      }

      // Check if we should attempt seamless sign-in (OTT present)
      if (_seamlessAuthService!.hasOTTInUrl()) {
        print('UnifiedAuthManager: OTT detected, waiting for seamless sign-in...');

        // Wait longer for the async sign-in to complete
        await Future.delayed(const Duration(milliseconds: 1500));

        if (_seamlessAuthService!.currentUser != null) {
          print('UnifiedAuthManager: Seamless sign-in successful');
          _authStateController.add(_seamlessAuthService!.currentUser);
          return true;
        } else {
          print('UnifiedAuthManager: Seamless sign-in failed - no user after processing');

          // Try one more time with a longer delay for slow networks
          await Future.delayed(const Duration(milliseconds: 1000));

          if (_seamlessAuthService!.currentUser != null) {
            print('UnifiedAuthManager: Seamless sign-in successful on retry');
            _authStateController.add(_seamlessAuthService!.currentUser);
            return true;
          }
        }
      }

      print('UnifiedAuthManager: Seamless authentication failed or not available');
      // Reset method since seamless failed
      _currentMethod = AuthenticationMethod.none;
      return false;
    } catch (e) {
      print('UnifiedAuthManager: Error during seamless authentication: $e');
      _currentMethod = AuthenticationMethod.none;
      return false;
    }
  }

  /// Initialize OAuth authentication
  Future<void> _initializeOAuthAuth() async {
    try {
      print('UnifiedAuthManager: Initializing OAuth authentication...');

      _authService ??= AuthService();
      await _authService!.initialize();

      _currentMethod = AuthenticationMethod.oauth;

      // Listen to OAuth auth state changes
      _authService!.authStateChanges.listen((user) {
        if (_currentMethod == AuthenticationMethod.oauth) {
          _authStateController.add(user);
        }
      });

      // Check if user is already authenticated via OAuth
      if (_authService!.currentUser != null) {
        _authStateController.add(_authService!.currentUser);
      }

      print('UnifiedAuthManager: OAuth authentication initialized');
    } catch (e) {
      print('UnifiedAuthManager: Error initializing OAuth auth: $e');
      _lastError = e.toString();
    }
  }

  /// Get last error from the current auth service
  String? get authServiceLastError {
    switch (_currentMethod) {
      case AuthenticationMethod.oauth:
        return _authService?.lastError;
      case AuthenticationMethod.seamless:
        return null; // SeamlessAuthService doesn't expose lastError
      case AuthenticationMethod.none:
        return _lastError;
    }
  }

  /// Check if this is a seamless authentication session
  bool get isSeamlessAuth => _currentMethod == AuthenticationMethod.seamless;

  /// Check if this is an OAuth authentication session
  bool get isOAuthAuth => _currentMethod == AuthenticationMethod.oauth;

  /// Force switch to OAuth authentication (useful for debugging or fallback)
  Future<void> switchToOAuth() async {
    if (_currentMethod == AuthenticationMethod.oauth) {
      print('UnifiedAuthManager: Already using OAuth authentication');
      return;
    }

    try {
      print('UnifiedAuthManager: Switching to OAuth authentication...');

      // Sign out from current method
      await signOut();

      // Reset state
      _currentMethod = AuthenticationMethod.none;
      _seamlessAttempted = true; // Prevent seamless retry

      // Initialize OAuth
      await _initializeOAuthAuth();

      print('UnifiedAuthManager: Successfully switched to OAuth authentication');
    } catch (e) {
      print('UnifiedAuthManager: Error switching to OAuth: $e');
      _lastError = e.toString();
    }
  }

  /// Force switch to seamless authentication (if available)
  Future<void> switchToSeamless() async {
    if (_currentMethod == AuthenticationMethod.seamless) {
      print('UnifiedAuthManager: Already using seamless authentication');
      return;
    }

    try {
      print('UnifiedAuthManager: Switching to seamless authentication...');

      // Check if seamless auth is available
      if (!await _detectWovenAppContext() && !await _hasExistingSeamlessTokens()) {
        print('UnifiedAuthManager: Seamless authentication not available');
        _lastError = 'Seamless authentication not available in current context';
        return;
      }

      // Sign out from current method
      await signOut();

      // Reset state
      _currentMethod = AuthenticationMethod.none;
      _seamlessAttempted = false; // Allow seamless retry

      // Attempt seamless auth
      final success = await _attemptSeamlessAuth();
      if (!success) {
        print('UnifiedAuthManager: Seamless authentication failed, falling back to OAuth');
        await _initializeOAuthAuth();
      }

      print('UnifiedAuthManager: Switch to seamless authentication completed');
    } catch (e) {
      print('UnifiedAuthManager: Error switching to seamless: $e');
      _lastError = e.toString();
      // Fallback to OAuth on error
      await _initializeOAuthAuth();
    }
  }

  /// Reset and reinitialize the authentication manager
  Future<void> reset() async {
    try {
      print('UnifiedAuthManager: Resetting authentication manager...');

      // Sign out from current method
      await signOut();

      // Reset all state
      _currentMethod = AuthenticationMethod.none;
      _preferredMethod = AuthenticationMethod.none;
      _seamlessAttempted = false;
      _lastError = null;
      _initialized = false;

      // Reinitialize
      await initialize();

      print('UnifiedAuthManager: Reset completed');
    } catch (e) {
      print('UnifiedAuthManager: Error during reset: $e');
      _lastError = e.toString();
    }
  }

  /// Get detailed status information for debugging
  Map<String, dynamic> getDebugInfo() {
    return {
      'currentMethod': _currentMethod.name,
      'preferredMethod': _preferredMethod.name,
      'initialized': _initialized,
      'seamlessAttempted': _seamlessAttempted,
      'lastError': _lastError,
      'hasSeamlessService': _seamlessAuthService != null,
      'hasOAuthService': _authService != null,
      'seamlessUser': _seamlessAuthService?.currentUser?.toString(),
      'oauthUser': _authService?.currentUser?.toString(),
      'currentUser': currentUser?.toString(),
      'currentUrl': kIsWeb ? Uri.base.toString() : 'N/A',
      'hasOTT': _seamlessAuthService?.hasOTTInUrl() ?? false,
      'seamlessAuthenticated': _seamlessAuthService?.isAuthenticated ?? false,
    };
  }

  /// Print comprehensive debug information
  void printDebugInfo() {
    final info = getDebugInfo();
    print('=== UnifiedAuthManager Debug Info ===');
    info.forEach((key, value) {
      print('$key: $value');
    });
    print('=====================================');
  }

  /// Test seamless authentication with a mock user (for testing purposes)
  Future<void> testSeamlessAuth() async {
    try {
      print('UnifiedAuthManager: Testing seamless authentication...');

      // Create a mock user for testing
      final mockUser = WovenUser(
        id: 'test-user-123',
        name: 'Test User',
        team: 'Test Team',
        lastActive: DateTime.now(),
      );

      // Set the current method to seamless
      _currentMethod = AuthenticationMethod.seamless;

      // Save the mock user
      await mockUser.save();

      // Emit the user through the auth state stream
      _authStateController.add(mockUser);

      print('UnifiedAuthManager: Test seamless authentication completed');
    } catch (e) {
      print('UnifiedAuthManager: Error during test seamless auth: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _authStateController.close();
    _seamlessAuthService?.dispose();
    // Note: Don't dispose _authService as it might be used elsewhere
  }
}

/// Enumeration of available authentication methods
enum AuthenticationMethod {
  none,
  seamless,  // OTT-based authentication from Woven App
  oauth,     // Direct OAuth flow with your client
}

extension AuthenticationMethodExtension on AuthenticationMethod {
  String get name {
    switch (this) {
      case AuthenticationMethod.none:
        return 'none';
      case AuthenticationMethod.seamless:
        return 'seamless';
      case AuthenticationMethod.oauth:
        return 'oauth';
    }
  }

  String get displayName {
    switch (this) {
      case AuthenticationMethod.none:
        return 'Not Authenticated';
      case AuthenticationMethod.seamless:
        return 'Woven App';
      case AuthenticationMethod.oauth:
        return 'Direct Login';
    }
  }
}
