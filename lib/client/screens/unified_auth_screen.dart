import 'dart:js' as js;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:scrum_poker/services/unified_auth_manager.dart';
import 'package:scrum_poker/client/widgets/version_info_widget.dart';

class UnifiedAuthScreen extends StatelessWidget {
  final bool isInitializing;
  final AuthenticationMethod? authMethod;
  final String? error;
  final VoidCallback? onLogin;
  final VoidCallback? onRetry;
  final VoidCallback? onSwitchToOAuth;

  const UnifiedAuthScreen({
    super.key,
    this.isInitializing = false,
    this.authMethod,
    this.error,
    this.onLogin,
    this.onRetry,
    this.onSwitchToOAuth,
  });

  @override
  Widget build(BuildContext context) {
    print('UnifiedAuthScreen: Building with isInitializing: $isInitializing, authMethod: ${authMethod?.name}, error: $error');

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // JUST THE FUCKING URL - NOTHING ELSE
            Container(
              width: double.infinity,
              height: 200,
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.yellow,
                border: Border.all(color: Colors.red, width: 5),
              ),
              child: SingleChildScrollView(
                child: Text(
                  'FULL URL (v2.0):\n${Uri.base.toString()}\n\nTIME: ${DateTime.now().toString()}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                    color: Colors.black,
                  ),
                ),
              ),
            ),

            // Debug information
            _buildDebugInfo(),

            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
              // App Icon
              CircleAvatar(
                radius: 50,
                backgroundColor: Colors.deepPurple,
                child: Icon(
                  _getIconForState(),
                  size: 50,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 30),

              // App Title
              const Text(
                'Scrum Poker',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
              ),
              const SizedBox(height: 8),

              // App Subtitle
              const Text(
                'Estimate user stories with your team',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 40),

              // Content based on state
              if (error != null)
                _buildErrorContent(context)
              else if (isInitializing)
                _buildInitializingContent()
              else
                _buildAuthContent(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForState() {
    if (error != null) {
      return Icons.error_outline;
    } else if (isInitializing) {
      return Icons.science; // Science icon for loading
    } else {
      return Icons.credit_card; // Credit card icon for auth
    }
  }

  Widget _buildErrorContent(BuildContext context) {
    return Column(
      children: [
        const Icon(
          Icons.error_outline,
          size: 48,
          color: Colors.red,
        ),
        const SizedBox(height: 16),
        Text(
          'Authentication Error',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        Text(
          error!,
          textAlign: TextAlign.center,
          style: const TextStyle(color: Colors.grey),
        ),
        const SizedBox(height: 24),
        ElevatedButton(
          onPressed: onRetry,
          child: const Text('Retry'),
        ),
      ],
    );
  }

  Widget _buildInitializingContent() {
    return const Column(
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurple),
        ),
        SizedBox(height: 24),
        Text(
          'Authenticating...',
          style: TextStyle(fontSize: 16),
        ),
        SizedBox(height: 8),
        Text(
          'Detecting authentication method',
          style: TextStyle(fontSize: 14, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildAuthContent(BuildContext context) {
    final method = authMethod ?? AuthenticationMethod.oauth;

    if (method == AuthenticationMethod.seamless) {
      return _buildSeamlessAuthContent();
    } else {
      return _buildOAuthContent();
    }
  }

  Widget _buildSeamlessAuthContent() {
    return Column(
      children: [
        const Icon(
          Icons.info_outline,
          size: 48,
          color: Colors.blue,
        ),
        const SizedBox(height: 16),
        const Text(
          'Seamless Authentication',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Authenticating with Woven App credentials...',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 24),
        const CircularProgressIndicator(),
        const SizedBox(height: 24),

        // Fallback options for seamless auth
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: onLogin,
              child: const Text('Retry'),
            ),
            const SizedBox(width: 16),
            TextButton(
              onPressed: onSwitchToOAuth,
              child: const Text('Use Direct Login'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOAuthContent() {
    return Column(
      children: [
        ElevatedButton.icon(
          onPressed: onLogin,
          icon: const Icon(Icons.login),
          label: const Text('Login with Woven'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.deepPurple,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            textStyle: const TextStyle(fontSize: 16),
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Sign in to access your team\'s poker sessions',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.grey,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  /// Build debug information widget to show on screen
  Widget _buildDebugInfo() {
    if (!kIsWeb) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.orange[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange[300]!, width: 2),
        ),
        child: const Text(
          '🔍 DEBUG: Not running on web',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
      );
    }

    try {
      final currentUrl = Uri.base.toString();
      final hasOTTInHash = currentUrl.contains('#ott=');
      final hasOTTInQuery = currentUrl.contains('?ott=') || currentUrl.contains('&ott=');
      final hasOTT = hasOTTInHash || hasOTTInQuery;
      final seamlessCompleted = js.context['seamlessAuthCompleted'] as bool?;
      final seamlessFailed = js.context['seamlessAuthFailed'] as bool?;
      final seamlessError = js.context['seamlessAuthError'] as String?;

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[400]!, width: 3),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🚨 DEBUG: WHY YOU SEE LOGIN SCREEN',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 12),
            // Break URL into parts to ensure we see everything
            _buildUrlParts(currentUrl),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: hasOTT ? Colors.green[100] : Colors.red[100],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: hasOTT ? Colors.green : Colors.red),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Has OTT Token: ${hasOTT ? '✅ YES' : '❌ NO'}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: hasOTT ? Colors.green[800] : Colors.red[800],
                    ),
                  ),
                  if (!hasOTT) ...[
                    const SizedBox(height: 4),
                    Text(
                      'In Hash: ${hasOTTInHash ? '✅' : '❌'} | In Query: ${hasOTTInQuery ? '✅' : '❌'}',
                      style: const TextStyle(fontSize: 11),
                    ),
                  ],
                ],
              ),
            ),
            Text(
              'Seamless Completed: ${seamlessCompleted == true ? '✅ YES' : '❌ NO'}',
              style: const TextStyle(fontSize: 11),
            ),
            Text(
              'Seamless Failed: ${seamlessFailed == true ? '❌ YES' : '✅ NO'}',
              style: const TextStyle(fontSize: 11),
            ),
            if (seamlessError != null)
              Text(
                'Error: $seamlessError',
                style: const TextStyle(fontSize: 11, color: Colors.red),
              ),
            const SizedBox(height: 4),
            Text(
              hasOTT
                ? '✅ This should be seamless auth (check why it failed)'
                : '❌ This is NOT a Woven App launch (no OTT token)',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: hasOTT ? Colors.orange : Colors.red,
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      // Fallback debug info that doesn't rely on JavaScript
      final currentUrl = Uri.base.toString();
      final hasOTT = currentUrl.contains('#ott=');

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.orange[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange[400]!, width: 3),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🚨 FALLBACK DEBUG (JS Error)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            SelectableText(
              'FULL URL: $currentUrl',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            SelectableText(
              'HASH: ${Uri.base.fragment.isEmpty ? 'EMPTY' : Uri.base.fragment}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            SelectableText(
              'QUERY: ${Uri.base.query.isEmpty ? 'EMPTY' : Uri.base.query}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: hasOTT ? Colors.green[100] : Colors.red[100],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: hasOTT ? Colors.green : Colors.red),
              ),
              child: Text(
                'Has OTT Token: ${hasOTT ? '✅ YES' : '❌ NO'}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: hasOTT ? Colors.green[800] : Colors.red[800],
                ),
              ),
            ),
            Text(
              'JS Error: $e',
              style: const TextStyle(color: Colors.red, fontSize: 11),
            ),
          ],
        ),
      );
    }
  }

  /// Build URL parts broken down into smaller, visible chunks
  Widget _buildUrlParts(String currentUrl) {
    try {
      final uri = Uri.base;

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.yellow[100],
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.orange[400]!, width: 2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'URL LENGTH: ${currentUrl.length} characters',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.red),
            ),
            const SizedBox(height: 8),

            // Show URL in 20-character lines
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.black, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'COMPLETE URL (20 chars per line):',
                    style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  ...() {
                    List<Widget> lines = [];
                    for (int i = 0; i < currentUrl.length; i += 20) {
                      int end = (i + 20 < currentUrl.length) ? i + 20 : currentUrl.length;
                      String chunk = currentUrl.substring(i, end);
                      lines.add(
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 2),
                          margin: const EdgeInsets.symmetric(vertical: 1),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            border: Border.all(color: Colors.grey[400]!),
                          ),
                          child: Text(
                            '${i.toString().padLeft(3, '0')}: $chunk',
                            style: const TextStyle(fontSize: 8, fontFamily: 'monospace'),
                          ),
                        ),
                      );
                    }
                    return lines;
                  }(),
                ],
              ),
            ),

            const SizedBox(height: 8),

            // Raw URL dump - this MUST show everything
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red[100],
                border: Border.all(color: Colors.red[500]!, width: 2),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'RAW URL DUMP:',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.red),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.black),
                    ),
                    child: Text(
                      currentUrl,
                      style: const TextStyle(fontSize: 7, fontFamily: 'monospace'),
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                border: Border.all(color: Colors.blue[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'PARSED COMPONENTS:',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blue),
                  ),
                  Text(
                    'Host: ${uri.host}',
                    style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                  ),
                  Text(
                    'Path: ${uri.path}',
                    style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                  ),
                  Text(
                    'Query Length: ${uri.query.length}',
                    style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                  ),
                  Text(
                    'Hash Length: ${uri.fragment.length}',
                    style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                  ),
                ],
              ),
            ),

            // Break query into parts
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: uri.query.isNotEmpty ? Colors.green[50] : Colors.red[50],
                border: Border.all(color: uri.query.isNotEmpty ? Colors.green[300]! : Colors.red[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'QUERY PARAMETERS (${uri.queryParameters.length}):',
                    style: const TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
                  ),
                  if (uri.query.isNotEmpty) ...[
                    ...uri.queryParameters.entries.map((entry) {
                      return Container(
                        margin: const EdgeInsets.symmetric(vertical: 1),
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: SelectableText(
                          '${entry.key}: ${entry.value}',
                          style: const TextStyle(fontSize: 9, fontFamily: 'monospace'),
                        ),
                      );
                    }).toList(),
                  ] else ...[
                    const Text(
                      'NO QUERY PARAMETERS',
                      style: TextStyle(fontSize: 10, color: Colors.red, fontWeight: FontWeight.bold),
                    ),
                  ],
                ],
              ),
            ),

            // Break hash into parts if it exists
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: uri.fragment.isNotEmpty ? Colors.purple[50] : Colors.red[50],
                border: Border.all(color: uri.fragment.isNotEmpty ? Colors.purple[300]! : Colors.red[300]!, width: 2),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'HASH FRAGMENT (${uri.fragment.length} chars):',
                    style: const TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
                  ),
                  if (uri.fragment.isNotEmpty) ...[
                    // Break hash into 25-character chunks
                    ...() {
                      List<String> hashChunks = [];
                      String hash = uri.fragment;
                      for (int i = 0; i < hash.length; i += 25) {
                        int end = (i + 25 < hash.length) ? i + 25 : hash.length;
                        hashChunks.add(hash.substring(i, end));
                      }
                      return hashChunks.asMap().entries.map((entry) {
                        int index = entry.key;
                        String chunk = entry.value;
                        int startPos = index * 25;
                        int endPos = startPos + chunk.length - 1;
                        return Container(
                          margin: const EdgeInsets.symmetric(vertical: 1),
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: SelectableText(
                            'Hash[$startPos-$endPos]: $chunk',
                            style: const TextStyle(fontSize: 8, fontFamily: 'monospace'),
                          ),
                        );
                      }).toList();
                    }(),
                  ] else ...[
                    const Text(
                      'NO HASH FRAGMENT (OTT SHOULD BE HERE!)',
                      style: TextStyle(fontSize: 10, color: Colors.red, fontWeight: FontWeight.bold),
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 8),
            const Text(
              'OTT DETECTION:',
              style: TextStyle(fontSize: 11, fontWeight: FontWeight.bold, color: Colors.red),
            ),
            SelectableText(
              'Contains #ott=: ${currentUrl.contains('#ott=')}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            SelectableText(
              'Contains ?ott=: ${currentUrl.contains('?ott=')}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            SelectableText(
              'Contains &ott=: ${currentUrl.contains('&ott=')}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            SelectableText(
              'Fragment contains ott=: ${uri.fragment.contains('ott=')}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            SelectableText(
              'Query contains ott: ${uri.queryParameters.containsKey('ott')}',
              style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.orange[100],
                border: Border.all(color: Colors.orange[400]!, width: 2),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'OTEL PARAMETERS (Alternative Token Source):',
                    style: TextStyle(fontSize: 11, fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                  Text(
                    'otel_platform_id: ${uri.queryParameters.containsKey('otel_platform_id') ? '✅ YES' : '❌ NO'}',
                    style: const TextStyle(fontSize: 10),
                  ),
                  Text(
                    'otel_session_id: ${uri.queryParameters.containsKey('otel_session_id') ? '✅ YES' : '❌ NO'}',
                    style: const TextStyle(fontSize: 10),
                  ),
                  Text(
                    'otel_app_id: ${uri.queryParameters.containsKey('otel_app_id') ? '✅ YES' : '❌ NO'}',
                    style: const TextStyle(fontSize: 10),
                  ),
                  if (uri.queryParameters.containsKey('otel_platform_id')) ...[
                    const SizedBox(height: 4),
                    SelectableText(
                      'otel_platform_id: ${uri.queryParameters['otel_platform_id']}',
                      style: const TextStyle(fontSize: 8, fontFamily: 'monospace'),
                    ),
                  ],
                  if (uri.queryParameters.containsKey('otel_session_id')) ...[
                    SelectableText(
                      'otel_session_id: ${uri.queryParameters['otel_session_id']}',
                      style: const TextStyle(fontSize: 8, fontFamily: 'monospace'),
                    ),
                  ],
                  if (uri.queryParameters.containsKey('otel_app_id')) ...[
                    SelectableText(
                      'otel_app_id: ${uri.queryParameters['otel_app_id']}',
                      style: const TextStyle(fontSize: 8, fontFamily: 'monospace'),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      return Text('Error parsing URL: $e');
    }
  }
}
