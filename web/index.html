<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Scrum Poker Application">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="scrum_poker">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <!-- OIDC Client Library -->
  <script
          src="https://cdnjs.cloudflare.com/ajax/libs/oidc-client-ts/3.2.0/browser/oidc-client-ts.min.js"
          integrity="sha512-4WvN6LN5SisjQTSwLVQP5YkenfPaDw14mbbXaQLphSy0+37dP/uRveXcCzUW0KpQQGNrlKey2UkOeJiNC9jTWw=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
  </script>

  <!-- JWT Decode Library for token parsing -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jwt-decode/3.1.2/jwt-decode.min.js"></script>

  <!-- Load LOCAL Woven SDK Implementation (fallback) -->
  <script src="woven-sdk/auth.js"></script>

  <!-- Load OFFICIAL Woven SDK as ES Module -->
  <script type="module">
    try {
      const { MiniAppAuthClient } = await import('./woven_app_sdk/dist/auth/index.mjs');
      // Make it globally available for the rest of the scripts
      window.OfficialMiniAppAuthClient = MiniAppAuthClient;
      console.log('✅ Official Woven SDK loaded:', typeof window.OfficialMiniAppAuthClient);
    } catch (error) {
      console.log('⚠️ Official Woven SDK not available:', error.message);
      console.log('⚠️ Will use local SDK implementation');
    }
  </script>

  <title>Scrum Poker</title>
  <link rel="manifest" href="manifest.json">

  <!-- Official Woven SDK Seamless Login Implementation -->
  <script>
    // Use the local SDK implementation that follows the official pattern

    // Global status flags
    window.seamlessAuthCompleted = false;
    window.seamlessAuthFailed = false;
    window.seamlessAuthError = null;

    // Helper function to extract OTT from URL
    function extractOTTFromURL() {
      try {
        console.log('🔍 Extracting OTT from URL...');
        console.log('🔍 Full URL:', window.location.href);
        console.log('🔍 Hash:', window.location.hash);

        // Parse the hash fragment
        const hash = window.location.hash;
        if (!hash || hash.length <= 1) {
          console.log('❌ No hash fragment found');
          return null;
        }

        // Remove the # and parse as URLSearchParams
        const hashParams = new URLSearchParams(hash.substring(1));
        const ott = hashParams.get('ott');

        if (ott) {
          console.log('✅ OTT found:', ott.substring(0, 20) + '...');
          return ott;
        } else {
          console.log('❌ No OTT parameter in hash');
          console.log('🔍 Available hash params:', Array.from(hashParams.keys()));
          return null;
        }
      } catch (error) {
        console.error('❌ Error extracting OTT:', error);
        return null;
      }
    }

    // Official implementation following documentation exactly
    async function initializeSeamlessAuth() {
      try {
        console.log('=== 🚀 Woven SDK: Starting Seamless Authentication ===');
        console.log('🔍 Current URL:', window.location.href);
        console.log('🔍 URL Hash:', window.location.hash);
        console.log('🔍 URL Search:', window.location.search);
        console.log('🔍 User Agent:', navigator.userAgent);
        console.log('🔍 Referrer:', document.referrer);

        // First, check if we have an OTT token in the URL
        const ottToken = extractOTTFromURL();
        if (!ottToken) {
          console.log('❌ CRITICAL: No OTT token found - this is NOT a Woven App launch!');
          console.log('❌ This explains why you see login screens instead of seamless auth');
          window.seamlessAuthFailed = true;
          window.seamlessAuthError = 'No OTT token - not launched from Woven App';
          return;
        }

        console.log('✅ OTT token found - proceeding with seamless authentication');
        console.log('🎫 OTT token length:', ottToken.length, 'characters');
        console.log('🎫 OTT token preview:', ottToken.substring(0, 50) + '...');

        // Try to use the official SDK first
        let authClient = null;
        let useLocalSDK = false;

        // Wait for the official SDK to load (ES module loading is async)
        let retries = 0;
        while (typeof window.OfficialMiniAppAuthClient === 'undefined' && retries < 10) {
          console.log('⏳ Waiting for official Woven SDK to load... (attempt ' + (retries + 1) + ')');
          await new Promise(resolve => setTimeout(resolve, 100));
          retries++;
        }

        if (typeof window.OfficialMiniAppAuthClient === 'undefined') {
          console.log('⚠️ Official Woven SDK not available, falling back to local implementation');
          useLocalSDK = true;
        } else {
          console.log('✅ Official Woven SDK loaded successfully');

          try {
            // Check if we should attempt sign-in first
            authClient = new window.OfficialMiniAppAuthClient({
              env: 'stage', // Change to 'prod' for production
              useAutoRefresh: true
            });

            console.log('✅ Official AuthClient created successfully');
            console.log('🔍 Should sign in from window location:', authClient.shouldSignInFromWindowLocation());

            // Make authClient globally available
            window.authClient = authClient;

            // Call this at app's entry point, preferably before rendering
            console.log('🚀 Calling official signInFromWindowLocation...');
            await authClient.signInFromWindowLocation();

            console.log('✅ Official signInFromWindowLocation completed');

            // Access user credentials as documented
            if (authClient.tokens && authClient.tokens.accessToken) {
              console.log('🎉 Official SDK authentication successful!');
              console.log('🔑 Token summary:', {
                idToken: authClient.tokens.idToken ? 'Present' : 'Missing',
                accessToken: authClient.tokens.accessToken ? 'Present' : 'Missing',
                refreshToken: authClient.tokens.refreshToken ? 'Present' : 'Missing'
              });

              // Store tokens for Flutter access
              window.wovenTokens = {
                idToken: authClient.tokens.idToken,
                accessToken: authClient.tokens.accessToken,
                refreshToken: authClient.tokens.refreshToken
              };

              // Set success flag
              window.seamlessAuthCompleted = true;
              console.log('🎉 Seamless auth completed successfully with official SDK');
            } else {
              console.error('❌ No tokens available after official signInFromWindowLocation');
              console.error('❌ Official AuthClient.tokens:', authClient.tokens);
              throw new Error('No tokens available after official sign-in');
            }
          } catch (officialSDKError) {
            console.error('❌ Official SDK failed:', officialSDKError);
            console.log('⚠️ Falling back to local SDK implementation');
            useLocalSDK = true;
          }
        }

        // Use local SDK implementation if official SDK failed or is not available
        if (useLocalSDK) {
          console.log('🔄 Using local SDK implementation...');

          // Use the local MiniAppAuthClient from woven-sdk/auth.js
          if (typeof window.MiniAppAuthClient === 'undefined') {
            console.error('❌ Local SDK also not available');
            console.error('❌ Available window properties:', Object.keys(window).filter(k => k.includes('Mini') || k.includes('Auth')));
            throw new Error('Neither official nor local SDK available');
          }

          console.log('✅ Local SDK available, creating client...');
          authClient = new window.MiniAppAuthClient({
            env: 'stage',
            useAutoRefresh: true
          });

          console.log('✅ Local AuthClient created successfully');
          window.authClient = authClient;

          console.log('🚀 Calling local signInFromWindowLocation...');
          console.log('🔍 About to exchange OTT token with Woven API...');

          try {
            await authClient.signInFromWindowLocation();
            console.log('✅ Local signInFromWindowLocation completed without throwing');
          } catch (signInError) {
            console.error('❌ Local signInFromWindowLocation threw error:', signInError);
            console.error('❌ Error details:', {
              message: signInError.message,
              stack: signInError.stack,
              name: signInError.name
            });
            throw signInError;
          }

          console.log('🔍 Checking tokens after local signInFromWindowLocation...');
          console.log('🔍 authClient.tokens exists:', !!authClient.tokens);
          if (authClient.tokens) {
            console.log('🔍 authClient.tokens keys:', Object.keys(authClient.tokens));
            console.log('🔍 accessToken exists:', !!authClient.tokens.accessToken);
            console.log('🔍 accessToken length:', authClient.tokens.accessToken ? authClient.tokens.accessToken.length : 'N/A');
          }

          if (authClient.tokens && authClient.tokens.accessToken) {
            console.log('🎉 Local SDK authentication successful!');
            console.log('🔑 Local token summary:', {
              idToken: authClient.tokens.idToken ? 'Present' : 'Missing',
              accessToken: authClient.tokens.accessToken ? 'Present' : 'Missing',
              refreshToken: authClient.tokens.refreshToken ? 'Present' : 'Missing'
            });

            // Store tokens for Flutter access
            window.wovenTokens = {
              idToken: authClient.tokens.idToken,
              accessToken: authClient.tokens.accessToken,
              refreshToken: authClient.tokens.refreshToken
            };

            window.seamlessAuthCompleted = true;
            console.log('🎉 Seamless auth completed successfully with local SDK');
            console.log('🎉 wovenTokens stored for Flutter access');
          } else {
            console.error('❌ Local SDK authentication failed - no tokens');
            console.error('❌ Local AuthClient.tokens:', authClient.tokens);
            console.error('❌ This means the OTT exchange with Woven API failed');
            throw new Error('Local SDK authentication failed - no tokens available after OTT exchange');
          }
        }

      } catch (error) {
        console.error('❌ Seamless authentication failed:', error);
        console.error('❌ Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        window.seamlessAuthFailed = true;
        window.seamlessAuthError = error.message || 'Authentication failed';
      }

      console.log('=== 📊 Woven SDK: Authentication Summary ===');
      console.log('📱 App Version: v1.1.0 (Build 2) - OTT Parsing Fix');
      console.log('⏰ Session Start:', new Date().toISOString());
      console.log('✅ Completed:', window.seamlessAuthCompleted || false);
      console.log('❌ Failed:', window.seamlessAuthFailed || false);
      console.log('🚨 Error:', window.seamlessAuthError || 'None');
      console.log('🔧 Has authClient:', !!window.authClient);
      console.log('🔑 Has wovenTokens:', !!window.wovenTokens);
      if (window.wovenTokens) {
        console.log('🔑 Token details:', {
          accessToken: window.wovenTokens.accessToken ? 'Present (' + window.wovenTokens.accessToken.length + ' chars)' : 'Missing',
          refreshToken: window.wovenTokens.refreshToken ? 'Present' : 'Missing',
          idToken: window.wovenTokens.idToken ? 'Present' : 'Missing'
        });
      }
      console.log('================================================');
    }

    // Initialize immediately when DOM is loaded (before Flutter app starts)
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeSeamlessAuth);
    } else {
      initializeSeamlessAuth();
    }
  </script>

  <!-- Debug script to log page load and URL info -->
  <script>
    // Immediate debug logging
    console.log('🚀 SCRUM POKER APP STARTING');
    console.log('📱 Version: v1.1.0 (Build 2) - OTT Parsing Fix');
    console.log('⏰ Load Time:', new Date().toISOString());
    console.log('🔍 URL Analysis:');
    console.log('  - Full URL:', window.location.href);
    console.log('  - Hash:', window.location.hash);
    console.log('  - Search:', window.location.search);
    console.log('  - Has OTT:', window.location.hash.includes('ott='));
    console.log('  - User Agent:', navigator.userAgent);
    console.log('  - Referrer:', document.referrer);

    // Check OTEL parameters
    const urlParams = new URLSearchParams(window.location.search);
    console.log('🔍 OTEL Parameters:');
    console.log('  - otel_platform_id:', urlParams.get('otel_platform_id') ? 'Present' : 'Missing');
    console.log('  - otel_session_id:', urlParams.get('otel_session_id') ? 'Present' : 'Missing');
    console.log('  - otel_app_id:', urlParams.get('otel_app_id') ? 'Present' : 'Missing');

    // Check JWT decode availability
    console.log('🔍 JWT Decode Library:');
    console.log('  - window.jwt_decode:', typeof window.jwt_decode);
    console.log('  - window.jwtDecode:', typeof window.jwtDecode);
    console.log('  - global jwt_decode:', typeof jwt_decode);

    // Check if this looks like a Woven App launch
    const isWovenAppLaunch = window.location.hash.includes('ott=') ||
                            urlParams.has('otel_platform_id') ||
                            document.referrer.includes('woven') ||
                            navigator.userAgent.includes('WovenApp');
    console.log('🎯 Is Woven App Launch:', isWovenAppLaunch);

    if (!isWovenAppLaunch) {
      console.log('❌ NOT a Woven App launch - expect to see login screens');
    } else {
      console.log('✅ Looks like Woven App launch - should see seamless auth');
    }

    window.addEventListener('load', function() {
      console.log('📱 Page fully loaded at:', new Date().toISOString());
      console.log('📊 Final Auth Status:');
      console.log('  - Seamless auth completed:', window.seamlessAuthCompleted || false);
      console.log('  - Seamless auth failed:', window.seamlessAuthFailed || false);
      if (window.seamlessAuthError) {
        console.log('  - Seamless auth error:', window.seamlessAuthError);
      }

      // Final JWT decode check
      console.log('📊 Final JWT Decode Status:');
      console.log('  - window.jwt_decode:', typeof window.jwt_decode);
      console.log('  - window.jwtDecode:', typeof window.jwtDecode);
    });
  </script>
</head>
<body>
<!-- Flutter will replace this with its app -->
<script src="flutter_bootstrap.js" async></script>
</body>
</html>