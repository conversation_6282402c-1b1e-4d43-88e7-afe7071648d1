<!DOCTYPE html>
<html>
<head>
    <title>Version Test</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .version-box { 
            background: #f0f0f0; 
            border: 2px solid #333; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
        }
        .highlight { background: yellow; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🚀 Scrum Poker - Version Information</h1>
    
    <div class="version-box">
        <h2>📱 App Version</h2>
        <p><span class="highlight">Version: v1.1.0</span></p>
        <p><span class="highlight">Build: 2</span></p>
        <p><span class="highlight">Feature: OTT Parsing Fix</span></p>
        <p>Load Time: <span id="loadTime"></span></p>
    </div>

    <div class="version-box">
        <h2>🔍 URL Analysis</h2>
        <p>Full URL: <span id="fullUrl"></span></p>
        <p>Hash: <span id="urlHash"></span></p>
        <p>Search: <span id="urlSearch"></span></p>
        <p>Has OTT: <span id="hasOtt"></span></p>
    </div>

    <div class="version-box">
        <h2>🎯 OTT Parsing Test</h2>
        <p>OTT Token: <span id="ottToken"></span></p>
        <p>Parsing Status: <span id="parsingStatus"></span></p>
    </div>

    <div class="version-box">
        <h2>🔧 Environment</h2>
        <p>User Agent: <span id="userAgent"></span></p>
        <p>Referrer: <span id="referrer"></span></p>
        <p>Platform: <span id="platform"></span></p>
    </div>

    <script>
        // Set load time
        document.getElementById('loadTime').textContent = new Date().toISOString();
        
        // URL analysis
        document.getElementById('fullUrl').textContent = window.location.href;
        document.getElementById('urlHash').textContent = window.location.hash || 'EMPTY';
        document.getElementById('urlSearch').textContent = window.location.search || 'EMPTY';
        
        // OTT detection
        const hasOtt = window.location.hash.includes('ott=');
        document.getElementById('hasOtt').textContent = hasOtt ? '✅ YES' : '❌ NO';
        document.getElementById('hasOtt').style.color = hasOtt ? 'green' : 'red';
        
        // OTT parsing test
        function extractOTT() {
            try {
                const hash = window.location.hash;
                if (!hash || hash.length <= 1) return null;
                
                const hashParams = new URLSearchParams(hash.substring(1));
                return hashParams.get('ott');
            } catch (error) {
                return 'ERROR: ' + error.message;
            }
        }
        
        const ottToken = extractOTT();
        if (ottToken) {
            if (ottToken.startsWith('ERROR:')) {
                document.getElementById('ottToken').textContent = ottToken;
                document.getElementById('ottToken').style.color = 'red';
                document.getElementById('parsingStatus').textContent = '❌ FAILED';
                document.getElementById('parsingStatus').style.color = 'red';
            } else {
                document.getElementById('ottToken').textContent = ottToken.substring(0, 20) + '...';
                document.getElementById('parsingStatus').textContent = '✅ SUCCESS';
                document.getElementById('parsingStatus').style.color = 'green';
            }
        } else {
            document.getElementById('ottToken').textContent = 'Not found';
            document.getElementById('parsingStatus').textContent = '⚪ N/A (no OTT in URL)';
        }
        
        // Environment info
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('referrer').textContent = document.referrer || 'EMPTY';
        document.getElementById('platform').textContent = navigator.platform;
        
        // Console logging
        console.log('🚀 VERSION TEST PAGE LOADED');
        console.log('📱 Version: v1.1.0 (Build 2) - OTT Parsing Fix');
        console.log('⏰ Load Time:', new Date().toISOString());
        console.log('🔍 OTT Status:', hasOtt ? 'DETECTED' : 'NOT FOUND');
        if (ottToken && !ottToken.startsWith('ERROR:')) {
            console.log('🎫 OTT Token:', ottToken.substring(0, 20) + '...');
        }
    </script>
</body>
</html>
